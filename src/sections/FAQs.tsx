'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { 
  HelpCircle, 
  Plus, 
  Minus, 
  MessageCircle,
  Mail,
  Phone,
  ExternalLink
} from 'lucide-react';

interface FloatingParticleProps {
  delay: number;
  duration: number;
  x: number;
  y: number;
}

const FloatingParticle: React.FC<FloatingParticleProps> = ({ delay, duration, x, y }) => (
  <motion.div
    className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-violet-400 rounded-full opacity-30"
    style={{ left: x, top: y }}
    animate={{
      y: [0, -30, 0],
      x: [0, 15, -15, 0],
      opacity: [0.3, 0.8, 0.3],
      scale: [1, 1.2, 1]
    }}
    transition={{
      duration,
      delay,
      ease: "easeInOut"
    }}
  />
);

interface FAQItemProps {
  question: string;
  answer: string;
  delay: number;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer, delay }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8, delay }}
      className="group"
    >
      <motion.div
        className="relative bg-white/[0.05] backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden shadow-xl shadow-purple-500/5 group-hover:shadow-purple-500/15 transition-all duration-500"
        whileHover={{
          boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.2)"
        }}
      >
        <motion.button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full !p-8 text-left flex items-center justify-between group/btn"
          whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.02)" }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="text-lg md:text-xl font-semibold text-white group-hover/btn:text-transparent group-hover/btn:bg-gradient-to-r group-hover/btn:from-purple-400 group-hover/btn:to-violet-400 group-hover/btn:bg-clip-text transition-all duration-300 !pr-4">
            {question}
          </h3>
          
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.3 }}
            className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-600/20 to-violet-600/20 border border-purple-400/30 flex items-center justify-center"
          >
            {isOpen ? (
              <Minus size={16} className="text-purple-400" />
            ) : (
              <Plus size={16} className="text-purple-400" />
            )}
          </motion.div>
        </motion.button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <div className="!px-8 !pb-8 !pt-0">
                <div className="h-px bg-gradient-to-r from-transparent via-purple-400/30 to-transparent !mb-6" />
                <p className="text-gray-300 leading-relaxed">
                  {answer}
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

interface SupportCardProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  title: string;
  description: string;
  action: string;
  gradient: string;
  delay: number;
}

const SupportCard: React.FC<SupportCardProps> = ({ icon: Icon, title, description, action, gradient, delay }) => (
  <motion.div
    initial={{ opacity: 0, y: 40, scale: 0.9 }}
    whileInView={{ opacity: 1, y: 0, scale: 1 }}
    viewport={{ once: true }}
    transition={{ duration: 0.8, delay }}
    whileHover={{ scale: 1.02, y: -5 }}
    className="group relative"
  >
    <motion.div
      className={`absolute inset-0 bg-gradient-to-r ${gradient} rounded-2xl blur-xl opacity-0 group-hover:opacity-40 transition-all duration-500`}
      whileHover={{ scale: 1.1 }}
    />
    
    <div className="relative bg-white/[0.05] backdrop-blur-xl border border-white/10 rounded-2xl !p-8 shadow-xl shadow-purple-500/5 group-hover:shadow-purple-500/15 transition-all duration-500">
      <motion.div
        className={`w-12 h-12 rounded-xl bg-gradient-to-r ${gradient} !p-3 !mb-6 shadow-lg`}
        whileHover={{ 
          scale: 1.1,
          rotate: 5,
          boxShadow: "0 20px 40px -12px rgba(147, 51, 234, 0.4)"
        }}
        transition={{ duration: 0.3 }}
      >
        <Icon size={24} className="text-white" />
      </motion.div>
      
      <h3 className="text-xl font-bold text-white !mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-violet-400 group-hover:bg-clip-text transition-all duration-300">
        {title}
      </h3>
      
      <p className="text-gray-300 leading-relaxed !mb-6">
        {description}
      </p>

      <motion.button
        className="flex items-center gap-2 text-purple-400 font-semibold hover:text-purple-300 transition-colors duration-300"
        whileHover={{ x: 5 }}
        transition={{ duration: 0.3 }}
      >
        {action}
        <ExternalLink size={16} />
      </motion.button>
    </div>
  </motion.div>
);

export const FAQs = () => {
  // Generate floating particles
  const particles = Array.from({ length: 12 }, (_, i) => ({
    id: i,
    delay: Math.random() * 4,
    duration: 6 + Math.random() * 3,
    x: Math.random() * 1200,
    y: Math.random() * 600,
  }));

  const faqs = [
    {
      question: "What is Toolnio and how does it work?",
      answer: "Toolnio is a comprehensive AI-powered platform that provides a suite of professional tools designed to streamline your workflow. Our tools use advanced AI technology to help you generate content, create documents, analyze data, and automate repetitive tasks with just a few clicks.",
      delay: 0
    },
    {
      question: "Is Toolnio free to use?",
      answer: "Yes! Toolnio offers a generous free tier that includes access to most of our core tools with basic usage limits. We also offer premium plans with enhanced features, higher usage limits, and priority support for users who need more advanced capabilities.",
      delay: 0.1
    },
    {
      question: "How secure is my data with Toolnio?",
      answer: "Data security is our top priority. We use enterprise-grade encryption, secure cloud infrastructure, and follow industry best practices to protect your information. We never store your personal data longer than necessary and never share it with third parties.",
      delay: 0.2
    },
    {
      question: "Can I use Toolnio for commercial purposes?",
      answer: "Absolutely! Toolnio is designed for both personal and commercial use. Many businesses, freelancers, and professionals rely on our tools for their daily operations. Our premium plans include commercial usage rights and additional features for business users.",
      delay: 0.3
    },
    {
      question: "What types of tools does Toolnio offer?",
      answer: "We offer a wide range of AI-powered tools including content generators, document creators, data analyzers, image processors, and productivity enhancers. Our toolkit covers everything from resume generation and invoice creation to text paraphrasing and name generation.",
      delay: 0.4
    },
    {
      question: "Do I need technical knowledge to use Toolnio?",
      answer: "Not at all! Toolnio is designed with simplicity in mind. Our intuitive interface makes it easy for anyone to use our tools, regardless of technical background. Most tools require just a few clicks or simple text input to generate professional results.",
      delay: 0.5
    },
    {
      question: "How often do you add new tools?",
      answer: "We're constantly innovating and adding new tools based on user feedback and emerging needs. We typically release new features and tools monthly, and we always announce updates through our newsletter and social media channels.",
      delay: 0.6
    },
    {
      question: "Can I integrate Toolnio with other platforms?",
      answer: "We're working on API integrations and third-party platform connections. Currently, most tools allow you to export results in various formats. Premium users get early access to new integration features as they become available.",
      delay: 0.7
    }
  ];

  const supportOptions = [
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Get instant help from our support team during business hours.",
      action: "Start Chat",
      gradient: "from-blue-500 to-cyan-500",
      delay: 0
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us detailed questions and we'll respond within 24 hours.",
      action: "Send Email",
      gradient: "from-green-500 to-emerald-500",
      delay: 0.1
    },
    {
      icon: Phone,
      title: "Phone Support",
      description: "Premium users can schedule phone calls with our experts.",
      action: "Schedule Call",
      gradient: "from-purple-500 to-pink-500",
      delay: 0.2
    }
  ];

  return (
    <section className="relative !py-32 !px-4 overflow-hidden">
      {/* Premium Radial Gradient Background */}
      <div className="absolute inset-0 bg-gradient-radial from-purple-900/30 via-gray-900 to-black" />
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/20 via-transparent to-violet-900/20" />
      
      {/* Floating Particles */}
      {particles.map((particle) => (
        <FloatingParticle
          key={particle.id}
          delay={particle.delay}
          duration={particle.duration}
          x={particle.x}
          y={particle.y}
        />
      ))}

      <div className="relative z-10 max-w-6xl mx-auto!">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="text-center !mb-20"
        >
          {/* Premium Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="!mb-8"
          >
            <motion.div
              className="inline-flex items-center gap-3 !px-6 !py-3 rounded-full bg-gradient-to-r from-purple-600/20 to-violet-600/20 border border-purple-400/30 backdrop-blur-xl shadow-2xl shadow-purple-500/20"
              whileHover={{ scale: 1.05, boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.4)" }}
            >
              <HelpCircle size={20} className="text-purple-400" />
              <span className="text-purple-300 font-semibold">Frequently Asked Questions</span>
            </motion.div>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-4xl md:text-6xl font-black !mb-8"
          >
            <span className="text-white">Got</span>{" "}
            <span className="text-transparent bg-gradient-to-r from-purple-400 via-violet-400 to-purple-400 bg-clip-text">
              Questions?
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto! leading-relaxed"
          >
            Find answers to the most common questions about Toolnio, our features, 
            pricing, and how to get the most out of our AI-powered tools.
          </motion.p>
        </motion.div>

        {/* FAQs List */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.8 }}
          className="space-y-6! !mb-24"
        >
          {faqs.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              delay={faq.delay}
            />
          ))}
        </motion.div>

        {/* Support Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-center !mb-16"
          >
            <h3 className="text-3xl md:text-4xl font-bold text-white !mb-4">
              Still Need <span className="text-transparent bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text">Help?</span>
            </h3>
            <p className="text-gray-300 text-lg">
              Our support team is here to help you succeed with Toolnio
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {supportOptions.map((option, index) => (
              <SupportCard
                key={option.title}
                icon={option.icon}
                title={option.title}
                description={option.description}
                action={option.action}
                gradient={option.gradient}
                delay={option.delay}
              />
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FAQs;
