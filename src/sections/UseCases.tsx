'use client';

import { motion } from 'framer-motion';
import { 
  Briefcase, 
  GraduationCap, 
  Palette, 
  Code, 
  Users, 
  TrendingUp, 
  Lightbulb, 
  Globe,
  ArrowRight,
  CheckCircle,
  Star
} from 'lucide-react';

interface FloatingParticleProps {
  delay: number;
  duration: number;
  x: number;
  y: number;
}

const FloatingParticle: React.FC<FloatingParticleProps> = ({ delay, duration, x, y }) => (
  <motion.div
    className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-violet-400 rounded-full opacity-30"
    style={{ left: x, top: y }}
    animate={{
      y: [0, -30, 0],
      x: [0, 15, -15, 0],
      opacity: [0.3, 0.8, 0.3],
      scale: [1, 1.2, 1]
    }}
    transition={{
      duration,
      delay,
      ease: "easeInOut"
    }}
  />
);

interface UseCaseCardProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  title: string;
  description: string;
  features: string[];
  gradient: string;
  delay: number;
  imageUrl?: string;
}

const UseCaseCard: React.FC<UseCaseCardProps> = ({ 
  icon: Icon, 
  title, 
  description, 
  features, 
  gradient, 
  delay,
  imageUrl = "https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=300&fit=crop&crop=center"
}) => (
  <motion.div
    initial={{ opacity: 0, y: 60, scale: 0.9 }}
    whileInView={{ opacity: 1, y: 0, scale: 1 }}
    viewport={{ once: true, margin: "-100px" }}
    transition={{ duration: 0.8, delay, ease: [0.25, 0.46, 0.45, 0.94] }}
    whileHover={{
      scale: 1.02,
      y: -10,
      transition: { duration: 0.3 }
    }}
    className="group relative"
  >
    {/* Enhanced Card Glow Effect */}
    <motion.div
      className={`absolute inset-0 bg-gradient-to-r ${gradient} rounded-3xl blur-2xl opacity-0 group-hover:opacity-60 transition-all duration-500`}
      whileHover={{ scale: 1.1 }}
    />
    <motion.div
      className={`absolute inset-0 bg-gradient-to-r ${gradient} rounded-3xl blur-xl opacity-10 group-hover:opacity-40 transition-all duration-500`}
      whileHover={{ scale: 1.05 }}
    />
    
    {/* Main Card */}
    <div className="relative bg-white/[0.08] backdrop-blur-xl border border-white/20 rounded-3xl overflow-hidden shadow-2xl shadow-purple-500/10 group-hover:shadow-purple-500/20 transition-all duration-500">
      {/* Image Header */}
      <div className="relative h-48 overflow-hidden">
        <motion.img
          src={imageUrl}
          alt={title}
          className="w-full h-full object-cover"
          whileHover={{ scale: 1.1 }}
          transition={{ duration: 0.5 }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        
        {/* Icon Overlay */}
        <motion.div
          className={`absolute top-4 right-4 w-12 h-12 rounded-xl bg-gradient-to-r ${gradient} !p-3 shadow-lg backdrop-blur-sm`}
          whileHover={{ 
            scale: 1.1,
            rotate: 5,
            boxShadow: "0 20px 40px -12px rgba(147, 51, 234, 0.4)"
          }}
          transition={{ duration: 0.3 }}
        >
          <Icon size={24} className="text-white" />
        </motion.div>
      </div>

      {/* Content */}
      <div className="!p-8">
        <h3 className="text-2xl font-bold text-white !mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-violet-400 group-hover:bg-clip-text transition-all duration-300">
          {title}
        </h3>
        
        <p className="text-gray-300 leading-relaxed !mb-6 group-hover:text-gray-200 transition-colors duration-300">
          {description}
        </p>

        {/* Features List */}
        <div className="space-y-3 !mb-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: delay + (index * 0.1) }}
              className="flex items-center gap-3"
            >
              <CheckCircle size={16} className="text-purple-400 flex-shrink-0" />
              <span className="text-gray-300 text-sm">{feature}</span>
            </motion.div>
          ))}
        </div>

        {/* CTA Button */}
        <motion.button
          className="group/btn w-full !py-3 !px-6 bg-gradient-to-r from-purple-600/20 to-violet-600/20 border border-purple-400/30 rounded-xl text-purple-300 font-semibold hover:from-purple-600/30 hover:to-violet-600/30 hover:text-white transition-all duration-300"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <span className="flex items-center justify-center gap-2">
            Explore Tools
            <motion.div
              animate={{ x: [0, 3, 0] }}
              transition={{ duration: 1.5 }}
            >
              <ArrowRight size={16} />
            </motion.div>
          </span>
        </motion.button>
      </div>
    </div>
  </motion.div>
);

interface TestimonialCardProps {
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  delay: number;
  avatar?: string;
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({ 
  name, 
  role, 
  company, 
  content, 
  rating, 
  delay,
  avatar = "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
}) => (
  <motion.div
    initial={{ opacity: 0, y: 40, scale: 0.9 }}
    whileInView={{ opacity: 1, y: 0, scale: 1 }}
    viewport={{ once: true }}
    transition={{ duration: 0.8, delay }}
    whileHover={{ scale: 1.02, y: -5 }}
    className="group relative"
  >
    <motion.div
      className="relative bg-white/[0.05] backdrop-blur-xl border border-white/10 rounded-2xl !p-8 shadow-xl shadow-purple-500/5 group-hover:shadow-purple-500/15 transition-all duration-500"
      whileHover={{
        boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.2)"
      }}
    >
      {/* Rating Stars */}
      <div className="flex gap-1 !mb-4">
        {Array.from({ length: 5 }, (_, i) => (
          <Star
            key={i}
            size={16}
            className={`${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}`}
          />
        ))}
      </div>

      {/* Content */}
      <p className="text-gray-300 leading-relaxed !mb-6 italic">
        "{content}"
      </p>

      {/* Author */}
      <div className="flex items-center gap-4">
        <motion.img
          src={avatar}
          alt={name}
          className="w-12 h-12 rounded-full object-cover border-2 border-purple-400/30"
          whileHover={{ scale: 1.1 }}
          transition={{ duration: 0.3 }}
        />
        <div>
          <h4 className="text-white font-semibold">{name}</h4>
          <p className="text-gray-400 text-sm">{role} at {company}</p>
        </div>
      </div>
    </motion.div>
  </motion.div>
);

export const UseCases = () => {
  // Generate floating particles
  const particles = Array.from({ length: 15 }, (_, i) => ({
    id: i,
    delay: Math.random() * 4,
    duration: 6 + Math.random() * 3,
    x: Math.random() * 1200,
    y: Math.random() * 800,
  }));

  const useCases = [
    {
      icon: Briefcase,
      title: "Business Professionals",
      description: "Streamline your workflow with AI-powered tools for document generation, data analysis, and professional communication.",
      features: [
        "Generate professional resumes and cover letters",
        "Create invoices and business documents",
        "Analyze data and generate reports",
        "Automate repetitive tasks"
      ],
      gradient: "from-blue-500 to-cyan-500",
      imageUrl: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&crop=center"
    },
    {
      icon: GraduationCap,
      title: "Students & Educators",
      description: "Enhance learning and teaching with intelligent tools for research, writing, and academic content creation.",
      features: [
        "Research assistance and citation tools",
        "Essay and paper writing support",
        "Study material generation",
        "Academic formatting tools"
      ],
      gradient: "from-green-500 to-emerald-500",
      imageUrl: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=400&h=300&fit=crop&crop=center"
    },
    {
      icon: Palette,
      title: "Content Creators",
      description: "Unleash your creativity with tools designed for writers, designers, and digital content creators.",
      features: [
        "Content ideation and brainstorming",
        "Social media content generation",
        "Creative writing assistance",
        "Brand name and slogan creation"
      ],
      gradient: "from-purple-500 to-pink-500",
      imageUrl: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=300&fit=crop&crop=center"
    },
    {
      icon: Code,
      title: "Developers & Tech Teams",
      description: "Accelerate development with AI tools for code generation, documentation, and technical writing.",
      features: [
        "Code documentation generation",
        "API documentation tools",
        "Technical writing assistance",
        "Project planning templates"
      ],
      gradient: "from-indigo-500 to-purple-500",
      imageUrl: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop&crop=center"
    },
    {
      icon: TrendingUp,
      title: "Marketing Teams",
      description: "Boost your marketing efforts with AI-driven content creation and campaign optimization tools.",
      features: [
        "Marketing copy generation",
        "Campaign planning tools",
        "Social media scheduling",
        "Performance analytics"
      ],
      gradient: "from-orange-500 to-red-500",
      imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop&crop=center"
    },
    {
      icon: Users,
      title: "Small Businesses",
      description: "Empower your small business with affordable AI tools that compete with enterprise solutions.",
      features: [
        "Customer communication tools",
        "Business plan generation",
        "Financial document creation",
        "Market research assistance"
      ],
      gradient: "from-teal-500 to-blue-500",
      imageUrl: "https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=300&fit=crop&crop=center"
    }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Marketing Director",
      company: "TechCorp",
      content: "Toolnio has revolutionized our content creation process. What used to take hours now takes minutes.",
      rating: 5,
      delay: 0
    },
    {
      name: "Michael Chen",
      role: "Freelance Writer",
      company: "Independent",
      content: "The AI writing tools are incredibly intuitive. They've helped me deliver better content to my clients.",
      rating: 5,
      delay: 0.1
    },
    {
      name: "Emily Rodriguez",
      role: "Startup Founder",
      company: "InnovateLab",
      content: "As a small business, Toolnio gives us access to enterprise-level AI tools at an affordable price.",
      rating: 5,
      delay: 0.2
    }
  ];

  return (
    <section className="relative !py-32 !px-4 overflow-hidden">
      {/* Premium Radial Gradient Background */}
      <div className="absolute inset-0 bg-gradient-radial from-indigo-900/40 via-gray-900 to-black" />
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/30 via-transparent to-violet-900/20" />

      {/* Floating Particles */}
      {particles.map((particle) => (
        <FloatingParticle
          key={particle.id}
          delay={particle.delay}
          duration={particle.duration}
          x={particle.x}
          y={particle.y}
        />
      ))}

      <div className="relative z-10 max-w-7xl mx-auto!">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="text-center !mb-20"
        >
          {/* Premium Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="!mb-8"
          >
            <motion.div
              className="inline-flex items-center gap-3 !px-6 !py-3 rounded-full bg-gradient-to-r from-purple-600/20 to-violet-600/20 border border-purple-400/30 backdrop-blur-xl shadow-2xl shadow-purple-500/20"
              whileHover={{ scale: 1.05, boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.4)" }}
            >
              <Lightbulb size={20} className="text-purple-400" />
              <span className="text-purple-300 font-semibold">Use Cases</span>
            </motion.div>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-4xl md:text-6xl font-black !mb-8"
          >
            <span className="text-white">Perfect for</span>
            <br />
            <span className="text-transparent bg-gradient-to-r from-purple-400 via-violet-400 to-purple-400 bg-clip-text">
              Every Professional
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-xl text-gray-300 max-w-4xl mx-auto! leading-relaxed"
          >
            From startups to enterprises, students to professionals – discover how Toolnio's AI-powered
            tools can transform your workflow and boost productivity across every industry.
          </motion.p>
        </motion.div>

        {/* Use Cases Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.8 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 !mb-32"
        >
          {useCases.map((useCase, index) => (
            <UseCaseCard
              key={useCase.title}
              icon={useCase.icon}
              title={useCase.title}
              description={useCase.description}
              features={useCase.features}
              gradient={useCase.gradient}
              delay={index * 0.1}
              imageUrl={useCase.imageUrl}
            />
          ))}
        </motion.div>

        {/* Testimonials Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="!mb-20"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-center !mb-16"
          >
            <h3 className="text-3xl md:text-4xl font-bold text-white !mb-4">
              What Our <span className="text-transparent bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text">Users Say</span>
            </h3>
            <p className="text-gray-300 text-lg">
              Real feedback from professionals who've transformed their workflow with Toolnio
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard
                key={testimonial.name}
                name={testimonial.name}
                role={testimonial.role}
                company={testimonial.company}
                content={testimonial.content}
                rating={testimonial.rating}
                delay={testimonial.delay}
              />
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="relative"
        >
          <motion.div
            className="relative bg-gradient-to-r from-purple-600/10 to-violet-600/10 backdrop-blur-xl border border-white/10 rounded-3xl !p-12 md:!p-16 shadow-2xl shadow-purple-500/10 text-center"
            whileHover={{
              boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.2)"
            }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="!mb-8"
            >
              <div className="w-20 h-20 rounded-full bg-gradient-to-r from-purple-500 to-violet-500 flex items-center justify-center mx-auto! !mb-6 shadow-lg shadow-purple-500/30">
                <Globe size={40} className="text-white" />
              </div>
            </motion.div>

            <motion.h3
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-3xl md:text-4xl font-bold text-white !mb-6"
            >
              Ready to <span className="text-transparent bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text">Transform</span> Your Workflow?
            </motion.h3>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto! leading-relaxed !mb-10"
            >
              Join thousands of professionals who've already discovered the power of AI-driven productivity.
              Start your journey today – it's free to get started.
            </motion.p>

            <motion.button
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="group !px-12 !py-5 bg-gradient-to-r from-purple-600 via-violet-600 to-purple-600 text-white font-bold text-lg rounded-3xl overflow-hidden shadow-2xl shadow-purple-500/30"
              whileHover={{
                scale: 1.05,
                boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.6)"
              }}
              whileTap={{ scale: 0.98 }}
            >
              <span className="relative z-10 flex items-center gap-3">
                Get Started Free
                <motion.div
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5 }}
                >
                  <ArrowRight size={20} />
                </motion.div>
              </span>
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default UseCases;
