'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import {
  Menu,
  X,
  Wrench,
  Info,
  Lightbulb,
  HelpCircle,
  Mail,
  ChevronDown,
  ImageIcon,
  Code,
  MapPin,
  FileText,
  Zap
} from 'lucide-react';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isToolsDropdownOpen, setIsToolsDropdownOpen] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.mobile-menu') && !target.closest('.mobile-menu-button')) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, []);

  const navItems = [
    {
      name: 'Tools',
      icon: Wrench,
      hasDropdown: true,
      dropdownItems: [
        { name: 'JSON Beautifier', icon: Code, href: '/tools/json-beautifier' },
        { name: 'Image Compressor', icon: ImageIcon, href: '/tools/image-compressor' },
        { name: 'Array → JSON Converter', icon: Code, href: '/tools/array-converter' },
        { name: 'Coordinate Calculator', icon: MapPin, href: '/tools/coordinate-calculator' },
        { name: 'Coordinate to Address', icon: MapPin, href: '/tools/coordinate-address' },
        { name: 'Invoice Maker', icon: FileText, href: '/tools/invoice-maker' },
        { name: 'More Tools', icon: Zap, href: '/tools' },
      ]
    },
    { name: 'About', icon: Info, href: '/about' },
    { name: 'Use Cases', icon: Lightbulb, href: '/use-cases' },
    { name: 'FAQs', icon: HelpCircle, href: '/faqs' },
    { name: 'Contact', icon: Mail, href: '/contact' },
  ];

  return (
    <>
      {/* Main Navigation */}
      <motion.nav
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${isScrolled
            ? 'backdrop-blur-xl bg-black/20 border-b border-white/10'
            : 'backdrop-blur-md bg-black/10'
          }`}
        style={{
          background: isScrolled
            ? 'linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(139,92,246,0.05) 50%, rgba(0,0,0,0.3) 100%)'
            : 'linear-gradient(135deg, rgba(0,0,0,0.2) 0%, rgba(139,92,246,0.03) 50%, rgba(0,0,0,0.2) 100%)'
        }}
      >
        <div className="max-w-7xl !mx-auto px-4! sm:px-6! lg:px-8!">
          <div className="flex items-center justify-between h-16 lg:h-20">

            {/* Logo Section */}
            <motion.div
              className="flex items-center !space-x-3"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <Image
                src="/logo.png"
                alt="Toolnio Logo"
                width={100}
                height={50}
                className="w-full h-full object-cover"
                priority
              />
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center !space-x-8">
              {navItems.map((item, index) => (
                <div key={item.name} className="relative group">
                  {item.hasDropdown ? (
                    <motion.button
                      className="flex items-center !space-x-1 text-gray-300 hover:text-white transition-colors duration-200 py-2"
                      onMouseEnter={() => setIsToolsDropdownOpen(true)}
                      onMouseLeave={() => setIsToolsDropdownOpen(false)}
                      whileHover={{ y: -1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <item.icon className="w-4 h-4" />
                      <span className="font-medium">{item.name}</span>
                      <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${isToolsDropdownOpen ? 'rotate-180' : ''
                        }`} />
                    </motion.button>
                  ) : (
                    <motion.a
                      href={item.href}
                      className="flex items-center !space-x-2 text-gray-300 hover:text-white transition-colors duration-200 py-2"
                      whileHover={{ y: -1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <item.icon className="w-4 h-4" />
                      <span className="font-medium">{item.name}</span>
                    </motion.a>
                  )}

                  {/* Tools Dropdown */}
                  {item.hasDropdown && (
                    <AnimatePresence>
                      {isToolsDropdownOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: 10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: 10, scale: 0.95 }}
                          transition={{ duration: 0.2, ease: "easeOut" }}
                          className="absolute top-full left-0 mt-2 w-64 rounded-xl backdrop-blur-3xl bg-black/50 border-b border border-white/20 shadow-2xl overflow-hidden"
                          onMouseEnter={() => setIsToolsDropdownOpen(true)}
                          onMouseLeave={() => setIsToolsDropdownOpen(false)}
                        >
                          <div className="!p-2 !space-y-2">
                            {item.dropdownItems?.map((dropdownItem, dropdownIndex) => (
                              <motion.a
                                key={dropdownItem.name}
                                href={dropdownItem.href}
                                className="flex items-center !space-x-3 !p-3 rounded-lg text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-200 group"
                                whileHover={{ x: 4 }}
                                transition={{ duration: 0.2 }}
                              >
                                <dropdownItem.icon className="w-4 h-4 text-purple-400 group-hover:text-purple-300" />
                                <span className="font-medium">{dropdownItem.name}</span>
                              </motion.a>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
              ))}
            </div>

            {/* Explore Button & Mobile Menu Button */}
            <div className="flex items-center !space-x-4">
              {/* Explore Button */}
              <motion.button
                className="hidden sm:flex items-center !px-6 !py-2.5 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full text-white font-semibold text-sm hover:from-purple-600 hover:to-violet-600 transition-all duration-300 shadow-lg hover:shadow-purple-500/25"
                whileHover={{ scale: 1.05, y: -1 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
                style={{
                  filter: 'drop-shadow(0 4px 12px rgba(139, 92, 246, 0.3))'
                }}
              >
                <Zap className="w-4 h-4 !me-1.5" />
                Explore
              </motion.button>

              {/* Mobile Menu Button */}
              <motion.button
                className="lg:hidden mobile-menu-button !p-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors duration-200"
                onClick={() => setIsOpen(!isOpen)}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.1 }}
              >
                {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </motion.button>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm lg:hidden"
              onClick={() => setIsOpen(false)}
            />

            {/* Mobile Menu Panel */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
              className="mobile-menu fixed top-0 right-0 bottom-0 z-50 w-80 max-w-[85vw] backdrop-blur-xl bg-black/40 border-l border-white/10 lg:hidden overflow-y-auto"
            >
              <div className="!p-6">
                {/* Mobile Menu Header */}
                <div className="flex items-center justify-between !mb-8">
                  <div className="flex items-center !space-x-3">
                    <div className="relative w-8 h-8">
                      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 to-violet-500 !p-0.5">
                        <div className="w-full h-full rounded-full overflow-hidden bg-white/10 backdrop-blur-sm">
                          <Image
                            src="/logo.jpeg"
                            alt="Toolnio Logo"
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                    </div>
                    <span className="text-lg font-bold bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text text-transparent">
                      Toolnio
                    </span>
                  </div>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="!p-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors duration-200"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Mobile Navigation Items */}
                <div className="!space-y-2">
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      {item.hasDropdown ? (
                        <div className="!space-y-2">
                          <div className="flex items-center !space-x-3 !p-4 rounded-xl text-gray-300">
                            <item.icon className="w-5 h-5 text-purple-400" />
                            <span className="font-medium text-lg">{item.name}</span>
                          </div>
                          <div className="!ml-8 !space-y-1">
                            {item.dropdownItems?.map((dropdownItem) => (
                              <motion.a
                                key={dropdownItem.name}
                                href={dropdownItem.href}
                                className="flex items-center !space-x-3 !p-3 rounded-lg text-gray-400 hover:text-white hover:bg-white/5 transition-all duration-200"
                                whileHover={{ x: 4 }}
                                transition={{ duration: 0.2 }}
                                onClick={() => setIsOpen(false)}
                              >
                                <dropdownItem.icon className="w-4 h-4 text-purple-400" />
                                <span>{dropdownItem.name}</span>
                              </motion.a>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <motion.a
                          href={item.href}
                          className="flex items-center !space-x-3 !p-4 rounded-xl text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-200"
                          whileHover={{ x: 4 }}
                          transition={{ duration: 0.2 }}
                          onClick={() => setIsOpen(false)}
                        >
                          <item.icon className="w-5 h-5 text-purple-400" />
                          <span className="font-medium text-lg">{item.name}</span>
                        </motion.a>
                      )}
                    </motion.div>
                  ))}
                </div>

                {/* Mobile Explore Button */}
                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                  className="w-full !mt-8 !px-6 !py-3 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full text-white font-semibold hover:from-purple-600 hover:to-violet-600 transition-all duration-300 shadow-lg"
                  style={{
                    filter: 'drop-shadow(0 4px 12px rgba(139, 92, 246, 0.3))'
                  }}
                  onClick={() => setIsOpen(false)}
                >
                  Explore Tools
                </motion.button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default Navigation;
