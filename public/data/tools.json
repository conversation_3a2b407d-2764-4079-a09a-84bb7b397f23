{"tools": [{"id": "baby-name-generator", "title": "AI Baby Name Generator", "description": "Discover unique and meaningful names for your little one with AI-powered suggestions.", "route": "/tools/baby-name-generator", "category": "AI", "keywords": ["baby", "name", "generator", "ai", "unique", "meaningful"], "icon": "Baby", "gradient": "from-pink-500/20 to-purple-500/20", "iconBg": "from-pink-500 to-purple-500", "featured": true}, {"id": "resume-generator", "title": "AI Resume Generator", "description": "Create professional resumes instantly with AI-powered formatting and content optimization.", "route": "/tools/resume-generator", "category": "AI", "keywords": ["resume", "cv", "generator", "ai", "professional", "job"], "icon": "FileText", "gradient": "from-blue-500/20 to-violet-500/20", "iconBg": "from-blue-500 to-violet-500", "featured": true}, {"id": "paraphrase-tool", "title": "AI Paraphrase Tool", "description": "Rewrite and enhance your content while maintaining meaning and improving clarity.", "route": "/tools/paraphrase", "category": "AI", "keywords": ["paraphrase", "rewrite", "ai", "content", "text", "enhance"], "icon": "RotateCcw", "gradient": "from-green-500/20 to-teal-500/20", "iconBg": "from-green-500 to-teal-500", "featured": true}, {"id": "invoice-generator", "title": "Invoice Generator", "description": "Generate professional invoices quickly with customizable templates and automatic calculations.", "route": "/tools/invoice-generator", "category": "Business", "keywords": ["invoice", "bill", "business", "payment", "template"], "icon": "Receipt", "gradient": "from-orange-500/20 to-red-500/20", "iconBg": "from-orange-500 to-red-500", "featured": true}, {"id": "image-compressor", "title": "Image Compressor", "description": "Compress images without losing quality. Supports JPEG, PNG, WebP formats.", "route": "/tools/image-compressor", "category": "Image", "keywords": ["image", "compress", "optimize", "jpeg", "png", "webp"], "icon": "Image", "gradient": "from-purple-500/20 to-indigo-500/20", "iconBg": "from-purple-500 to-indigo-500", "featured": false}, {"id": "json-converter", "title": "JSON Converter", "description": "Convert JSON to CSV, XML, YAML and other formats easily.", "route": "/tools/json-converter", "category": "Data", "keywords": ["json", "convert", "csv", "xml", "yaml", "data"], "icon": "Code", "gradient": "from-cyan-500/20 to-blue-500/20", "iconBg": "from-cyan-500 to-blue-500", "featured": false}, {"id": "coordinate-calculator", "title": "Coordinate Calculator", "description": "Calculate distances, midpoints, and conversions between coordinate systems.", "route": "/tools/coordinate-calculator", "category": "Math", "keywords": ["coordinate", "calculate", "distance", "gps", "latitude", "longitude"], "icon": "MapPin", "gradient": "from-emerald-500/20 to-green-500/20", "iconBg": "from-emerald-500 to-green-500", "featured": false}, {"id": "qr-generator", "title": "QR Code Generator", "description": "Generate QR codes for URLs, text, WiFi, and more with customization options.", "route": "/tools/qr-generator", "category": "Utility", "keywords": ["qr", "code", "generator", "url", "wifi", "text"], "icon": "QrCode", "gradient": "from-slate-500/20 to-gray-500/20", "iconBg": "from-slate-500 to-gray-500", "featured": false}, {"id": "password-generator", "title": "Password Generator", "description": "Generate secure passwords with customizable length and character sets.", "route": "/tools/password-generator", "category": "Security", "keywords": ["password", "generate", "secure", "random", "strong"], "icon": "Shield", "gradient": "from-red-500/20 to-pink-500/20", "iconBg": "from-red-500 to-pink-500", "featured": false}, {"id": "color-palette", "title": "Color Palette Generator", "description": "Create beautiful color palettes and extract colors from images.", "route": "/tools/color-palette", "category": "Design", "keywords": ["color", "palette", "design", "hex", "rgb", "hsl"], "icon": "Palette", "gradient": "from-violet-500/20 to-purple-500/20", "iconBg": "from-violet-500 to-purple-500", "featured": false}, {"id": "text-formatter", "title": "Text Formatter", "description": "Format text with various transformations: uppercase, lowercase, title case, and more.", "route": "/tools/text-formatter", "category": "Text", "keywords": ["text", "format", "case", "transform", "uppercase", "lowercase"], "icon": "Type", "gradient": "from-amber-500/20 to-yellow-500/20", "iconBg": "from-amber-500 to-yellow-500", "featured": false}, {"id": "url-shortener", "title": "URL Shortener", "description": "Shorten long URLs and track click analytics with custom aliases.", "route": "/tools/url-shortener", "category": "Utility", "keywords": ["url", "shorten", "link", "short", "analytics"], "icon": "Link", "gradient": "from-teal-500/20 to-cyan-500/20", "iconBg": "from-teal-500 to-cyan-500", "featured": false}, {"id": "base64-encoder", "title": "Base64 Encoder/Decoder", "description": "Encode and decode Base64 strings with support for files and text.", "route": "/tools/base64-encoder", "category": "Data", "keywords": ["base64", "encode", "decode", "string", "file"], "icon": "Binary", "gradient": "from-indigo-500/20 to-blue-500/20", "iconBg": "from-indigo-500 to-blue-500", "featured": false}, {"id": "markdown-editor", "title": "Markdown Editor", "description": "Write and preview Markdown with live rendering and export options.", "route": "/tools/markdown-editor", "category": "Text", "keywords": ["markdown", "editor", "preview", "write", "export"], "icon": "FileEdit", "gradient": "from-rose-500/20 to-pink-500/20", "iconBg": "from-rose-500 to-pink-500", "featured": false}, {"id": "hash-generator", "title": "Hash Generator", "description": "Generate MD5, SHA-1, SHA-256 and other hash values for text and files.", "route": "/tools/hash-generator", "category": "Security", "keywords": ["hash", "md5", "sha", "checksum", "security"], "icon": "Hash", "gradient": "from-orange-500/20 to-amber-500/20", "iconBg": "from-orange-500 to-amber-500", "featured": false}]}